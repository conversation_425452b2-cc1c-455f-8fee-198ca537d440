import VectorStore from '../utils/vectorStore.js';
import { textExtractor } from '../utils/textExtractor.js';
import { createClient } from '@supabase/supabase-js';
import { GoogleGenerativeAI } from '@google/generative-ai';
import fs from 'fs/promises';
import path from 'path';
import pdf from 'pdf-parse';

export class DocumentService {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    this.vectorStore = new VectorStore();
  }

  async uploadDocument(file) {
    try {
      const text = await textExtractor.extract(file);
      const chunks = this.splitIntoChunks(text);
      
      const documentId = await this.saveDocumentMetadata(file);
      
      // Adicionar chunks ao vector store
      for (const chunk of chunks) {
        await this.vectorStore.addDocument(chunk, {
          documentId,
          fileName: file.name
        });
      }

      return { success: true, documentId };
    } catch (error) {
      this.logger.error('Erro ao fazer upload do documento:', error);
      throw error;
    }
  }

  /**
   * Processa um documento PDF e cria chunks para RAG
   */
  async processDocument(filePath, metadata = {}) {
    try {
      console.log(`📄 Iniciando processamento do documento: ${filePath}`);
      
      // 1. Extrair texto do PDF
      const pdfBuffer = await fs.readFile(filePath);
      const pdfData = await pdf(pdfBuffer);
      const content = pdfData.text;

      // 2. Salvar documento no banco
      const { data: document, error: docError } = await this.supabase
        .from('documents')
        .insert({
          title: metadata.title || path.basename(filePath),
          content: content,
          file_path: filePath,
          file_type: 'pdf',
          file_size: pdfBuffer.length,
          status: 'processing',
          metadata: metadata
        })
        .select()
        .single();

      if (docError) throw docError;

      // 3. Dividir em chunks
      const chunks = this.createChunks(content, {
        chunkSize: 1000,
        overlap: 200
      });

      // 4. Gerar embeddings e salvar chunks
      await this.processChunks(document.id, chunks);

      // 5. Atualizar status do documento
      await this.supabase
        .from('documents')
        .update({ 
          status: 'completed', 
          processed_at: new Date().toISOString() 
        })
        .eq('id', document.id);

      console.log(`✅ Documento processado com sucesso: ${document.id}`);
      return document;

    } catch (error) {
      console.error('❌ Erro ao processar documento:', error);
      throw error;
    }
  }

  /**
   * Divide o texto em chunks menores
   */
  createChunks(text, options = {}) {
    const { chunkSize = 1000, overlap = 200 } = options;
    const chunks = [];
    
    // Dividir por parágrafos primeiro
    const paragraphs = text.split(/\n\s*\n/);
    let currentChunk = '';
    let chunkIndex = 0;

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > chunkSize && currentChunk.length > 0) {
        chunks.push({
          text: currentChunk.trim(),
          index: chunkIndex++
        });
        
        // Manter overlap
        const words = currentChunk.split(' ');
        const overlapWords = words.slice(-Math.floor(overlap / 5)); // Aproximadamente
        currentChunk = overlapWords.join(' ') + ' ' + paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }

    // Adicionar último chunk
    if (currentChunk.trim()) {
      chunks.push({
        text: currentChunk.trim(),
        index: chunkIndex
      });
    }

    return chunks;
  }

  /**
   * Processa chunks e gera embeddings
   */
  async processChunks(documentId, chunks) {
    const batchSize = 5; // Processar em lotes para evitar rate limits
    
    for (let i = 0; i < chunks.length; i += batchSize) {
      const batch = chunks.slice(i, i + batchSize);
      
      await Promise.all(batch.map(async (chunk) => {
        try {
          // Gerar embedding usando Gemini
          const embedding = await this.generateEmbedding(chunk.text);
          
          // Salvar chunk no banco
          await this.supabase
            .from('document_chunks')
            .insert({
              document_id: documentId,
              chunk_text: chunk.text,
              chunk_index: chunk.index,
              embedding: embedding,
              metadata: {
                word_count: chunk.text.split(' ').length,
                char_count: chunk.text.length
              }
            });
            
        } catch (error) {
          console.error(`❌ Erro ao processar chunk ${chunk.index}:`, error);
        }
      }));
      
      // Pequena pausa entre lotes
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  /**
   * Gera embedding para um texto usando Gemini
   */
  async generateEmbedding(text) {
    try {
      // Usar Gemini para gerar embeddings
      const result = await this.model.embedContent(text);
      return result.embedding.values;
    } catch (error) {
      console.error('❌ Erro ao gerar embedding:', error);
      throw error;
    }
  }

  /**
   * Busca semântica nos documentos
   */
  async semanticSearch(query, options = {}) {
    const { limit = 5, threshold = 0.7 } = options;
    
    try {
      // 1. Gerar embedding da query
      const queryEmbedding = await this.generateEmbedding(query);
      
      // 2. Buscar chunks similares
      const { data: results, error } = await this.supabase.rpc(
        'search_similar_chunks',
        {
          query_embedding: queryEmbedding,
          similarity_threshold: threshold,
          match_count: limit
        }
      );

      if (error) throw error;

      return results || [];
      
    } catch (error) {
      console.error('❌ Erro na busca semântica:', error);
      throw error;
    }
  }

  // Implementar outros métodos auxiliares...
}