import fs from 'fs/promises';
import path from 'path';
import pdf from 'pdf-parse';
import mammoth from 'mammoth';
import { Logger } from './Logger.js';

/**
 * Extrator de Texto de Documentos
 * Suporta múltiplos formatos de arquivo
 */
export class TextExtractor {
  constructor() {
    this.logger = new Logger();
    
    // Formatos suportados
    this.supportedFormats = {
      pdf: { maxSize: 50 * 1024 * 1024 }, // 50MB
      docx: { maxSize: 20 * 1024 * 1024 }, // 20MB
      doc: { maxSize: 20 * 1024 * 1024 }, // 20MB
      txt: { maxSize: 10 * 1024 * 1024 }, // 10MB
      md: { maxSize: 10 * 1024 * 1024 }, // 10MB
      rtf: { maxSize: 10 * 1024 * 1024 } // 10MB
    };
  }

  /**
   * Extrair texto de um arquivo
   */
  async extract(file) {
    try {
      const fileInfo = await this.getFileInfo(file);
      
      // Validar arquivo
      await this.validateFile(fileInfo);
      
      // Extrair texto baseado no formato
      const text = await this.extractByFormat(fileInfo);
      
      this.logger.info(`✅ Texto extraído: ${fileInfo.name} (${text.length} caracteres)`);
      
      return {
        text,
        metadata: {
          fileName: fileInfo.name,
          fileSize: fileInfo.size,
          fileType: fileInfo.extension,
          extractedAt: new Date().toISOString(),
          characterCount: text.length,
          wordCount: this.countWords(text)
        }
      };
      
    } catch (error) {
      this.logger.error('❌ Erro ao extrair texto:', error);
      throw error;
    }
  }

  /**
   * Obter informações do arquivo
   */
  async getFileInfo(file) {
    if (typeof file === 'string') {
      // Caminho do arquivo
      const stats = await fs.stat(file);
      return {
        path: file,
        name: path.basename(file),
        extension: path.extname(file).toLowerCase().slice(1),
        size: stats.size,
        isBuffer: false
      };
    } else if (file.buffer || Buffer.isBuffer(file)) {
      // Buffer ou objeto com buffer
      const buffer = file.buffer || file;
      return {
        buffer,
        name: file.name || file.originalname || 'unknown',
        extension: this.getExtensionFromName(file.name || file.originalname || ''),
        size: buffer.length,
        isBuffer: true
      };
    } else if (file.path) {
      // Objeto de arquivo do multer
      const stats = await fs.stat(file.path);
      return {
        path: file.path,
        name: file.originalname || file.filename,
        extension: this.getExtensionFromName(file.originalname || file.filename),
        size: stats.size,
        isBuffer: false
      };
    } else {
      throw new Error('Formato de arquivo não suportado');
    }
  }

  /**
   * Validar arquivo
   */
  async validateFile(fileInfo) {
    const { extension, size } = fileInfo;
    
    // Verificar formato suportado
    if (!this.supportedFormats[extension]) {
      throw new Error(`Formato não suportado: ${extension}`);
    }
    
    // Verificar tamanho
    const maxSize = this.supportedFormats[extension].maxSize;
    if (size > maxSize) {
      throw new Error(`Arquivo muito grande. Máximo: ${Math.round(maxSize / 1024 / 1024)}MB`);
    }
    
    return true;
  }

  /**
   * Extrair texto baseado no formato
   */
  async extractByFormat(fileInfo) {
    const { extension } = fileInfo;
    
    switch (extension) {
      case 'pdf':
        return await this.extractFromPDF(fileInfo);
      case 'docx':
        return await this.extractFromDOCX(fileInfo);
      case 'doc':
        return await this.extractFromDOC(fileInfo);
      case 'txt':
        return await this.extractFromTXT(fileInfo);
      case 'md':
        return await this.extractFromMarkdown(fileInfo);
      case 'rtf':
        return await this.extractFromRTF(fileInfo);
      default:
        throw new Error(`Extração não implementada para: ${extension}`);
    }
  }

  /**
   * Extrair texto de PDF
   */
  async extractFromPDF(fileInfo) {
    try {
      let buffer;
      
      if (fileInfo.isBuffer) {
        buffer = fileInfo.buffer;
      } else {
        buffer = await fs.readFile(fileInfo.path);
      }
      
      const data = await pdf(buffer);
      return this.cleanText(data.text);
      
    } catch (error) {
      throw new Error(`Erro ao extrair PDF: ${error.message}`);
    }
  }

  /**
   * Extrair texto de DOCX
   */
  async extractFromDOCX(fileInfo) {
    try {
      let buffer;
      
      if (fileInfo.isBuffer) {
        buffer = fileInfo.buffer;
      } else {
        buffer = await fs.readFile(fileInfo.path);
      }
      
      const result = await mammoth.extractRawText({ buffer });
      return this.cleanText(result.value);
      
    } catch (error) {
      throw new Error(`Erro ao extrair DOCX: ${error.message}`);
    }
  }

  /**
   * Extrair texto de DOC (formato antigo)
   */
  async extractFromDOC(fileInfo) {
    try {
      // Para arquivos DOC antigos, tentar usar mammoth mesmo assim
      return await this.extractFromDOCX(fileInfo);
    } catch (error) {
      throw new Error(`Erro ao extrair DOC: ${error.message}. Considere converter para DOCX.`);
    }
  }

  /**
   * Extrair texto de TXT
   */
  async extractFromTXT(fileInfo) {
    try {
      let content;
      
      if (fileInfo.isBuffer) {
        content = fileInfo.buffer.toString('utf8');
      } else {
        content = await fs.readFile(fileInfo.path, 'utf8');
      }
      
      return this.cleanText(content);
      
    } catch (error) {
      throw new Error(`Erro ao extrair TXT: ${error.message}`);
    }
  }

  /**
   * Extrair texto de Markdown
   */
  async extractFromMarkdown(fileInfo) {
    try {
      let content;
      
      if (fileInfo.isBuffer) {
        content = fileInfo.buffer.toString('utf8');
      } else {
        content = await fs.readFile(fileInfo.path, 'utf8');
      }
      
      // Remover sintaxe markdown básica
      const cleanContent = content
        .replace(/^#{1,6}\s+/gm, '') // Headers
        .replace(/\*\*(.*?)\*\*/g, '$1') // Bold
        .replace(/\*(.*?)\*/g, '$1') // Italic
        .replace(/`(.*?)`/g, '$1') // Inline code
        .replace(/```[\s\S]*?```/g, '') // Code blocks
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Links
        .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1'); // Images
      
      return this.cleanText(cleanContent);
      
    } catch (error) {
      throw new Error(`Erro ao extrair Markdown: ${error.message}`);
    }
  }

  /**
   * Extrair texto de RTF
   */
  async extractFromRTF(fileInfo) {
    try {
      let content;
      
      if (fileInfo.isBuffer) {
        content = fileInfo.buffer.toString('utf8');
      } else {
        content = await fs.readFile(fileInfo.path, 'utf8');
      }
      
      // Remover códigos RTF básicos (implementação simplificada)
      const cleanContent = content
        .replace(/\\[a-z]+\d*\s?/g, '') // Comandos RTF
        .replace(/[{}]/g, '') // Chaves
        .replace(/\\\\/g, '\\') // Barras duplas
        .replace(/\\'/g, "'"); // Aspas
      
      return this.cleanText(cleanContent);
      
    } catch (error) {
      throw new Error(`Erro ao extrair RTF: ${error.message}`);
    }
  }

  /**
   * Limpar e normalizar texto
   */
  cleanText(text) {
    return text
      .replace(/\r\n/g, '\n') // Normalizar quebras de linha
      .replace(/\r/g, '\n') // Normalizar quebras de linha
      .replace(/\n{3,}/g, '\n\n') // Remover quebras excessivas
      .replace(/\s{2,}/g, ' ') // Remover espaços excessivos
      .trim(); // Remover espaços no início/fim
  }

  /**
   * Contar palavras
   */
  countWords(text) {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Obter extensão do nome do arquivo
   */
  getExtensionFromName(filename) {
    return path.extname(filename).toLowerCase().slice(1);
  }

  /**
   * Verificar se formato é suportado
   */
  isFormatSupported(extension) {
    return !!this.supportedFormats[extension.toLowerCase()];
  }

  /**
   * Obter formatos suportados
   */
  getSupportedFormats() {
    return Object.keys(this.supportedFormats);
  }

  /**
   * Obter estatísticas do extrator
   */
  getStats() {
    return {
      supportedFormats: this.getSupportedFormats(),
      maxSizes: Object.entries(this.supportedFormats).reduce((acc, [format, config]) => {
        acc[format] = `${Math.round(config.maxSize / 1024 / 1024)}MB`;
        return acc;
      }, {})
    };
  }
}

// Exportar instância singleton
export const textExtractor = new TextExtractor();
