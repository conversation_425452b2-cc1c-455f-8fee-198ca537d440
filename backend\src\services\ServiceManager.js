import { EventEmitter } from 'events';

// Importar serviços
import { WhatsAppServiceFactory } from './WhatsAppServiceFactory.js';
import { RAGService } from './RAGService.js';
import { MessageHandler } from './MessageHandler.js';
import { SessionManager } from './SessionManager.js';
import { PersistenceService } from './PersistenceService.js';
import { AntiBanService } from './AntiBanService.js';
import { HumanBehaviorService } from './HumanBehaviorService.js';
import RafaelaResponseService from './RafaelaResponseService.js';
import WPPConnectServer from '../../wppconnect-server.js';

/**
 * Gerenciador Centralizado de Serviços
 * Coordena a inicialização, configuração e ciclo de vida de todos os serviços
 */
export class ServiceManager extends EventEmitter {
  constructor(config, logger) {
    super();
    
    this.config = config;
    this.logger = logger;
    
    // Estado dos serviços
    this.services = new Map();
    this.isInitialized = false;
    this.initializationOrder = [
      'persistence',
      'session',
      'wppconnect',
      'whatsapp',
      'antiban',
      'humanBehavior',
      'rag',
      'rafaelaResponse',
      'messageHandler'
    ];
  }

  /**
   * Inicializar todos os serviços
   */
  async initializeAll() {
    if (this.isInitialized) {
      this.logger.warn('⚠️ Serviços já foram inicializados');
      return;
    }

    this.logger.info('🔧 Inicializando todos os serviços...');

    try {
      // Inicializar serviços na ordem correta
      for (const serviceName of this.initializationOrder) {
        await this.initializeService(serviceName);
      }

      this.isInitialized = true;
      this.logger.info('✅ Todos os serviços inicializados com sucesso');
      this.emit('allServicesInitialized');

    } catch (error) {
      this.logger.error('❌ Erro ao inicializar serviços:', error);
      throw error;
    }
  }

  /**
   * Inicializar serviço específico
   */
  async initializeService(serviceName) {
    try {
      this.logger.info(`🔄 Inicializando serviço: ${serviceName}`);

      let service;
      let initialized = false;

      switch (serviceName) {
        case 'persistence':
          service = new PersistenceService();
          await service.initialize();
          initialized = true;
          break;

        case 'session':
          service = new SessionManager();
          await service.initialize();
          initialized = true;
          break;

        case 'wppconnect':
          if (this.config.whatsapp.useHttp) {
            service = new WPPConnectServer();
            try {
              await service.start();
              initialized = true;
            } catch (error) {
              this.logger.warn(`⚠️ WPPConnect Server não pôde ser iniciado: ${error.message}`);
              initialized = false;
            }
          } else {
            this.logger.info('⏭️ WPPConnect Server não necessário para este modo');
            initialized = true;
          }
          break;

        case 'whatsapp':
          const whatsappFactory = new WhatsAppServiceFactory(this.config, this.logger);
          service = whatsappFactory.createService();
          
          try {
            await service.initialize();
            initialized = true;
            
            // Configurar listeners de eventos
            this.setupWhatsAppEventListeners(service);
          } catch (error) {
            this.logger.warn(`⚠️ WhatsApp Service em modo degradado: ${error.message}`);
            initialized = false;
          }
          break;

        case 'antiban':
          if (this.config.antiban.enabled) {
            service = new AntiBanService();
            await service.initialize();
            initialized = true;
          } else {
            this.logger.info('⏭️ Anti-ban desabilitado');
            initialized = true;
          }
          break;

        case 'humanBehavior':
          service = new HumanBehaviorService();
          await service.initialize();
          initialized = true;
          break;

        case 'rag':
          if (this.config.rag.enabled) {
            service = new RAGService();
            try {
              await service.initialize();
              initialized = true;
            } catch (error) {
              this.logger.warn(`⚠️ RAG Service em modo limitado: ${error.message}`);
              initialized = false;
            }
          } else {
            this.logger.info('⏭️ RAG desabilitado');
            initialized = true;
          }
          break;

        case 'rafaelaResponse':
          service = new RafaelaResponseService();
          await service.initialize();
          initialized = true;
          break;

        case 'messageHandler':
          const whatsappService = this.getService('whatsapp');
          const ragService = this.getService('rag');
          
          if (whatsappService) {
            service = new MessageHandler(whatsappService, ragService);
            service.setupHandlers();
            initialized = true;
          } else {
            this.logger.warn('⚠️ MessageHandler não pôde ser inicializado - WhatsApp Service não disponível');
            initialized = false;
          }
          break;

        default:
          throw new Error(`Serviço desconhecido: ${serviceName}`);
      }

      // Armazenar serviço se foi inicializado com sucesso
      if (service && initialized) {
        this.services.set(serviceName, {
          instance: service,
          initialized: true,
          startTime: new Date()
        });
        this.logger.info(`✅ Serviço ${serviceName} inicializado`);
      } else {
        this.services.set(serviceName, {
          instance: null,
          initialized: false,
          error: 'Falha na inicialização'
        });
        this.logger.warn(`⚠️ Serviço ${serviceName} não foi inicializado`);
      }

    } catch (error) {
      this.logger.error(`❌ Erro ao inicializar serviço ${serviceName}:`, error);
      
      this.services.set(serviceName, {
        instance: null,
        initialized: false,
        error: error.message
      });

      // Não fazer throw para permitir que outros serviços continuem
      // throw error;
    }
  }

  /**
   * Configurar listeners de eventos do WhatsApp
   */
  setupWhatsAppEventListeners(whatsappService) {
    whatsappService.on('qr', (qr) => {
      this.logger.info('📱 QR Code gerado');
      this.emit('whatsapp:qr', qr);
    });

    whatsappService.on('ready', () => {
      this.logger.info('✅ WhatsApp conectado e pronto');
      this.emit('whatsapp:ready');
    });

    whatsappService.on('disconnected', () => {
      this.logger.warn('⚠️ WhatsApp desconectado');
      this.emit('whatsapp:disconnected');
    });

    whatsappService.on('message', (message) => {
      this.logger.debug('📨 Nova mensagem recebida');
      this.emit('whatsapp:message', message);
    });

    whatsappService.on('syncStarted', () => {
      this.logger.info('🔄 Sincronização iniciada');
      this.emit('whatsapp:syncStarted');
    });

    whatsappService.on('syncCompleted', () => {
      this.logger.info('✅ Sincronização concluída');
      this.emit('whatsapp:syncCompleted');
    });
  }

  /**
   * Obter instância de um serviço
   */
  getService(serviceName) {
    const service = this.services.get(serviceName);
    return service?.initialized ? service.instance : null;
  }

  /**
   * Verificar se um serviço está disponível
   */
  isServiceAvailable(serviceName) {
    const service = this.services.get(serviceName);
    return service?.initialized === true;
  }

  /**
   * Obter status de todos os serviços
   */
  getServicesStatus() {
    const status = {};
    
    for (const [name, service] of this.services) {
      status[name] = {
        initialized: service.initialized,
        startTime: service.startTime,
        error: service.error
      };
    }
    
    return status;
  }

  /**
   * Parar todos os serviços
   */
  async stopAll() {
    this.logger.info('🛑 Parando todos os serviços...');

    // Parar serviços na ordem reversa
    const reverseOrder = [...this.initializationOrder].reverse();
    
    for (const serviceName of reverseOrder) {
      await this.stopService(serviceName);
    }

    this.services.clear();
    this.isInitialized = false;
    this.logger.info('✅ Todos os serviços parados');
  }

  /**
   * Parar serviço específico
   */
  async stopService(serviceName) {
    const service = this.services.get(serviceName);
    
    if (!service || !service.instance) {
      return;
    }

    try {
      this.logger.info(`🛑 Parando serviço: ${serviceName}`);
      
      if (typeof service.instance.stop === 'function') {
        await service.instance.stop();
      }
      
      this.logger.info(`✅ Serviço ${serviceName} parado`);
    } catch (error) {
      this.logger.error(`❌ Erro ao parar serviço ${serviceName}:`, error);
    }
  }

  /**
   * Reiniciar serviço específico
   */
  async restartService(serviceName) {
    this.logger.info(`🔄 Reiniciando serviço: ${serviceName}`);
    
    await this.stopService(serviceName);
    await new Promise(resolve => setTimeout(resolve, 1000));
    await this.initializeService(serviceName);
  }

  /**
   * Log do status dos serviços
   */
  logServiceStatus() {
    this.logger.info('📊 Status dos Serviços:');
    
    for (const [name, service] of this.services) {
      const status = service.initialized ? '✅' : '❌';
      const uptime = service.startTime ? 
        `(${Math.round((Date.now() - service.startTime.getTime()) / 1000)}s)` : '';
      
      this.logger.info(`   ${status} ${name} ${uptime}`);
      
      if (service.error) {
        this.logger.info(`      Erro: ${service.error}`);
      }
    }
  }

  /**
   * Health check de todos os serviços
   */
  async healthCheck() {
    const health = {};
    
    for (const [name, service] of this.services) {
      if (service.initialized && service.instance) {
        try {
          if (typeof service.instance.healthCheck === 'function') {
            health[name] = await service.instance.healthCheck();
          } else {
            health[name] = true; // Assume healthy if no health check method
          }
        } catch (error) {
          health[name] = false;
        }
      } else {
        health[name] = false;
      }
    }
    
    return health;
  }
}
