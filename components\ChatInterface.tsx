import React, { useState, useRef, useEffect } from 'react';
import type { Message } from '../types';

interface ChatInterfaceProps {
  messages: Message[];
  onSendMessage: (content: string) => void;
  isLoading: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement>;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  onSendMessage,
  isLoading,
  messagesEndRef
}) => {
  const [inputValue, setInputValue] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && !isLoading) {
      onSendMessage(inputValue.trim());
      setInputValue('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [inputValue]);

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="flex flex-col h-full bg-gh-bg">
      {/* Área de mensagens */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-12 animate-fade-in">
            <div className="w-12 h-12 mx-auto bg-gh-link rounded-full flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>

            <h3 className="text-lg font-semibold text-gh-text mb-2">
              Bem-vindo ao Assistente da Vereadora Rafaela de Nilda
            </h3>
            <p className="text-gh-text-secondary max-w-md mx-auto mb-6 text-sm leading-relaxed">
              Faça suas perguntas sobre projetos de lei, serviços públicos, ou qualquer assunto relacionado ao mandato.
            </p>

            {/* Sugestões de perguntas */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto">
              {[
                "Quais projetos de lei foram apresentados?",
                "Como está a situação da saúde em Parnamirim?",
                "Qual o status do transporte público?",
                "Há projetos ambientais em andamento?"
              ].map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => setInputValue(suggestion)}
                  className="p-3 text-left border border-gh-border rounded-md hover:bg-gh-hover transition-colors text-sm text-gh-text"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`message-bubble p-4 max-w-3xl ${
                message.sender === 'user' ? 'user-message' : 'bot-message'
              }`}>
                <div className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</div>

                {/* Mostrar sources se existirem */}
                {message.sources && message.sources.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gh-border">
                    <p className="text-xs text-gh-text-secondary mb-2 font-medium">Fontes consultadas:</p>
                    <div className="space-y-2">
                      {message.sources.map((source, index) => (
                        <div key={source.id} className="text-xs bg-gh-sidebar p-2 rounded border border-gh-border">
                          <span className="font-medium text-gh-text">{source.title}</span>
                          <p className="mt-1 text-gh-text-secondary">{source.content}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Mostrar confiança se existir */}
                {message.confidence !== undefined && (
                  <div className="mt-3 flex items-center space-x-2">
                    <span className="text-xs text-gh-text-secondary">Confiança:</span>
                    <div className="flex-1 bg-gh-border rounded-full h-1">
                      <div
                        className="bg-success h-1 rounded-full"
                        style={{ width: `${message.confidence * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-gh-text-secondary">
                      {Math.round(message.confidence * 100)}%
                    </span>
                  </div>
                )}

                <div className="text-xs mt-2 text-gh-text-secondary">
                  {formatTimestamp(message.timestamp)}
                </div>
              </div>
            </div>
          ))
        )}

        {/* Indicador de digitação */}
        {isLoading && (
          <div className="flex justify-start animate-fade-in">
            <div className="bot-message message-bubble p-4">
              <div className="flex items-center space-x-2">
                <div className="loading-dots">
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                </div>
                <span className="text-sm text-gh-text-secondary">Assistente está processando...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Área de input */}
      <div className="border-t border-gh-border bg-gh-bg p-4">
        <form onSubmit={handleSubmit}>
          <div className="flex items-end space-x-3">
            <div className="flex-1 relative">
              <textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Digite sua pergunta sobre a Câmara Municipal de Parnamirim..."
                className="input-modern w-full resize-none"
                rows={1}
                style={{ minHeight: '40px', maxHeight: '120px' }}
                disabled={isLoading}
              />

              {/* Contador de caracteres */}
              <div className="absolute bottom-2 right-3 text-xs text-gh-text-secondary">
                {inputValue.length}/1000
              </div>
            </div>
            <button
              type="submit"
              disabled={!inputValue.trim() || isLoading}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="loading-spinner"></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )}
              <span className="ml-2">Enviar</span>
            </button>
          </div>
        </form>

        {/* Dicas de uso */}
        <div className="mt-2 flex items-center justify-between text-xs text-gh-text-secondary">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <kbd className="px-1.5 py-0.5 bg-gh-sidebar rounded border border-gh-border text-xs">Enter</kbd>
              <span>Enviar</span>
            </div>
            <div className="hidden sm:flex items-center space-x-1">
              <kbd className="px-1.5 py-0.5 bg-gh-sidebar rounded border border-gh-border text-xs">Shift+Enter</kbd>
              <span>Nova linha</span>
            </div>
          </div>

          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-success rounded-full"></div>
            <span>IA Online</span>
          </div>
        </div>
      </div>
    </div>
  );
};
