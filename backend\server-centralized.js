import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { EventEmitter } from 'events';

// Importar configuração centralizada
import { ServerConfig } from './src/config/ServerConfig.js';
import { Logger } from './src/utils/Logger.js';

// Importar middlewares
import { loggingMiddleware } from './src/middleware/logging.js';
import { authenticateApiKey, verifyOrigin, rateLimitByIP, auditLog } from './src/middleware/auth.js';
import { errorHandler } from './src/middleware/errorHandler.js';

// Importar serviços
import { ServiceManager } from './src/services/ServiceManager.js';

// Importar rotas
import { RouteManager } from './src/routes/RouteManager.js';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '.env') });

/**
 * Servidor Backend Centralizado
 * Vereadora Rafaela de Nilda - Sistema RAG
 */
class CentralizedServer {
  constructor() {
    this.app = express();
    this.config = new ServerConfig();
    this.logger = new Logger();
    this.serviceManager = new ServiceManager(this.config, this.logger);
    this.routeManager = new RouteManager(this.serviceManager, this.logger);
    
    // Estado do servidor
    this.isInitialized = false;
    this.server = null;
    
    // Configurar limite de listeners para evitar memory leak
    process.setMaxListeners(30);
    EventEmitter.defaultMaxListeners = 30;
    
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddlewares() {
    this.logger.info('🔧 Configurando middlewares...');

    // Middleware de auditoria
    this.app.use(auditLog);

    // Verificação de origem (apenas para APIs sensíveis)
    if (this.config.security.enableOriginVerification) {
      this.app.use('/api/security', verifyOrigin);
    }

    // Segurança
    this.app.use(helmet({
      contentSecurityPolicy: this.config.security.enableCSP,
      crossOriginEmbedderPolicy: false
    }));

    // CORS
    this.app.use(cors({
      origin: this.config.cors.allowedOrigins,
      credentials: this.config.cors.credentials,
      methods: this.config.cors.methods,
      allowedHeaders: this.config.cors.allowedHeaders
    }));

    // Compressão
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: this.config.rateLimit.windowMs,
      max: this.config.rateLimit.maxRequests,
      message: {
        error: 'Muitas requisições. Tente novamente em alguns minutos.',
        code: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // Logging
    this.app.use(morgan('combined', {
      stream: { write: (message) => this.logger.info(message.trim()) }
    }));

    // Body parsing
    this.app.use(express.json({ limit: this.config.server.bodyLimit }));
    this.app.use(express.urlencoded({ 
      extended: true, 
      limit: this.config.server.bodyLimit 
    }));

    // Middleware de logging customizado
    this.app.use(loggingMiddleware);

    // Servir arquivos estáticos
    this.app.use('/static', express.static(join(__dirname, 'public')));
    
    this.logger.info('✅ Middlewares configurados');
  }

  setupRoutes() {
    this.logger.info('🛣️ Configurando rotas...');

    // Rota de boas-vindas
    this.app.get('/', (req, res) => {
      res.json({
        message: '🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda',
        version: this.config.app.version,
        status: 'online',
        timestamp: new Date().toISOString(),
        environment: this.config.app.environment,
        endpoints: {
          health: '/api/health',
          whatsapp: '/api/whatsapp',
          session: '/api/session',
          webhook: '/api/webhook',
          rag: '/api/rag',
          security: '/api/security',
          antiban: '/api/antiban'
        }
      });
    });

    // Configurar rotas através do RouteManager
    this.routeManager.setupRoutes(this.app);
    
    // Rota 404
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint não encontrado',
        message: 'A rota solicitada não existe neste servidor',
        availableEndpoints: [
          '/api/health',
          '/api/whatsapp',
          '/api/session',
          '/api/webhook',
          '/api/rag'
        ]
      });
    });
    
    this.logger.info('✅ Rotas configuradas');
  }

  setupErrorHandling() {
    this.logger.info('⚠️ Configurando tratamento de erros...');

    // Middleware de tratamento de erros
    this.app.use(errorHandler);

    // Remover listeners existentes para evitar duplicação
    process.removeAllListeners('unhandledRejection');
    process.removeAllListeners('uncaughtException');

    // Tratamento de promises rejeitadas
    process.once('unhandledRejection', (reason, promise) => {
      this.logger.error('Promise rejeitada não tratada:', reason);
      this.logger.error('Promise:', promise);
      this.logger.error('Stack trace:', reason?.stack);
    });

    // Tratamento de exceções não capturadas
    process.once('uncaughtException', (error) => {
      this.logger.error('Exceção não capturada:', error);
      this.logger.error('Stack trace:', error.stack);
      // Não fazer exit imediatamente para permitir debug
      setTimeout(() => process.exit(1), 1000);
    });
    
    this.logger.info('✅ Tratamento de erros configurado');
  }

  async initialize() {
    if (this.isInitialized) {
      this.logger.warn('⚠️ Servidor já foi inicializado');
      return;
    }

    try {
      this.logger.info('🔄 Inicializando servidor centralizado...');

      // Disponibilizar configurações para as rotas primeiro
      this.app.locals.config = this.config;
      this.app.locals.logger = this.logger;

      // Inicializar serviços de forma não-bloqueante
      this.logger.info('🔧 Iniciando serviços em background...');

      // Não aguardar a inicialização completa dos serviços
      this.serviceManager.initializeAll().catch(error => {
        this.logger.error('❌ Erro na inicialização dos serviços:', error);
      });

      // Disponibilizar service manager mesmo que não esteja totalmente inicializado
      this.app.locals.serviceManager = this.serviceManager;

      this.isInitialized = true;
      this.logger.info('✅ Servidor centralizado inicializado (serviços em background)');

    } catch (error) {
      this.logger.error('❌ Erro ao inicializar servidor:', error);
      throw error;
    }
  }

  async start() {
    try {
      // Inicializar se ainda não foi inicializado
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Iniciar servidor HTTP
      this.server = this.app.listen(this.config.server.port, () => {
        this.logger.info(`🚀 Servidor iniciado na porta ${this.config.server.port}`);
        this.logger.info(`🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda`);
        this.logger.info(`📱 Ambiente: ${this.config.app.environment}`);
        this.logger.info(`🌐 URL: http://localhost:${this.config.server.port}`);
        
        // Mostrar informações dos serviços
        this.serviceManager.logServiceStatus();
      });

      // Configurar graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      this.logger.error('❌ Erro ao iniciar servidor:', error);
      throw error;
    }
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      this.logger.info(`🛑 Recebido sinal ${signal}, iniciando shutdown graceful...`);
      
      try {
        // Parar de aceitar novas conexões
        if (this.server) {
          this.server.close();
        }

        // Parar serviços
        await this.serviceManager.stopAll();

        this.logger.info('✅ Shutdown graceful concluído');
        process.exit(0);
      } catch (error) {
        this.logger.error('❌ Erro durante shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  }

  async stop() {
    this.logger.info('🛑 Parando servidor...');

    try {
      // Parar servidor HTTP
      if (this.server) {
        this.server.close();
      }

      // Parar serviços
      await this.serviceManager.stopAll();

      this.logger.info('✅ Servidor parado com sucesso');
    } catch (error) {
      this.logger.error('❌ Erro ao parar servidor:', error);
    }
  }
}

export { CentralizedServer };

// Se executado diretamente, iniciar servidor
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new CentralizedServer();
  
  server.start().catch((error) => {
    console.error('Falha ao iniciar servidor:', error);
    process.exit(1);
  });
}
