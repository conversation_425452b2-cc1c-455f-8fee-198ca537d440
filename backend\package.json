{"name": "vereadora-rafaela-backend", "version": "1.0.0", "description": "Backend WhatsApp para Assistente Virtual da Vereadora Rafaela de Nilda", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "echo \"No tests yet\" && exit 0"}, "keywords": ["whatsapp", "wppconnect", "chatbot", "vereadora", "parnam<PERSON>m", "rag", "ai"], "author": "Sistema RAG Vereadora Rafaela de Nilda", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.18.2", "@supabase/supabase-js": "^2.38.0", "@google/generative-ai": "^0.1.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}